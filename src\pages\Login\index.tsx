import React, { useState } from "react";
import { Card, Form, Input, Button, message } from "antd";
import {
  UserOutlined,
  LockOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
} from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { login, saveToken } from "../../services/authService";
import Threads from "../../components/Threads";
import "./style.less";
import logo from "../../assets/images/logo.png";

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  // 默认设置为已验证，实际项目中应该与滑块组件关联
  const [sliderVerified] = useState(true);

  // 处理登录表单提交
  const handleSubmit = async (values: { email: string; password: string }) => {
    if (!sliderVerified) {
      message.error("请完成滑块验证");
      return;
    }

    try {
      setLoading(true);
      const response = await login({
        email: values.email,
        password: values.password,
      });

      // 保存token到本地存储
      saveToken(response.token);

      message.success("登录成功");

      // 登录成功后跳转到首页
      navigate("/");
    } catch (error) {
      console.error("登录失败:", error);
      // 错误处理已在请求拦截器中完成
    } finally {
      setLoading(false);
    }
  };

  // 注释掉未使用的滑块验证处理函数
  // 实际项目中应该在滑块组件上绑定此函数
  // const handleSliderVerify = () => {
  //   setSliderVerified(true);
  //   message.success("验证成功");
  // };

  return (
    <div className="login-container">
      <div className="login-background">
        <Threads amplitude={1} distance={0} enableMouseInteraction={true} />
      </div>
      <Card className="login-card">
        <div className="login-logo">
          <img src={logo} alt="Logo" />
        </div>

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          autoComplete="off"
          layout="vertical"
        >
          <Form.Item
            label="邮件"
            name="email"
            rules={[
              { required: true, message: "请输入邮箱" },
              { type: "email", message: "请输入有效的邮箱地址" },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="输入邮件"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="密码"
            name="password"
            rules={[{ required: true, message: "请输入密码" }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="输入密码"
              size="large"
              iconRender={(visible) =>
                visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
              }
            />
          </Form.Item>

          <Form.Item>
            <div className="slider-captcha">
              <div className="slider-track">
                <div className="slider-handler" id="slider-handler">
                  &gt;&gt;
                </div>
                <div className="slider-text">
                  Hold the slider and drag to the right
                </div>
              </div>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              block
              size="large"
              loading={loading}
              disabled={!sliderVerified}
              className="login-button"
            >
              登入
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default Login;
