import React from "react";
import { But<PERSON>, Layout, Menu } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { setCurrentPage, setSidebarCollapsed } from "@/store/slices/appSlice";
import SvgIcon from "@/components/SvgIcon";
import styles from "./index.module.less";
import logo from "@/assets/images/logo.png";

const { Sider } = Layout;

const Sidebar: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { sidebarCollapsed } = useAppSelector((state) => state.app);

  const currentMenuKey = `/${location.pathname.split("/")?.[1] || ""}`;
  const menuItems = [
    {
      key: "/",
      icon: (
        <SvgIcon
          name="home"
          size={20}
          color={currentMenuKey === "/" ? "#ffffff" : "#667085"}
        />
      ),
      label: t("navigation.home"),
    },
    {
      key: "/materials",
      icon: (
        <SvgIcon
          name="image"
          size={20}
          color={currentMenuKey === "/materials" ? "#fff" : "#667085"}
        />
      ),
      label: t("navigation.materials"),
    },
    {
      key: "/copywriting",
      icon: (
        <SvgIcon
          name="file"
          size={20}
          color={currentMenuKey === "/copywriting" ? "#fff" : "#667085"}
        />
      ),
      label: t("navigation.copywriting"),
    },
    {
      key: "/ai-copywriting",
      icon: (
        <SvgIcon
          name="ai-icon"
          size={20}
          color={currentMenuKey === "/ai-copywriting" ? "#fff" : "#667085"}
        />
      ),
      label: t("navigation.aiCopywriting"),
    },
    {
      key: "/qrcode",
      icon: (
        <SvgIcon
          name="qr-code"
          size={20}
          color={currentMenuKey === "/qrcode" ? "#fff" : "#667085"}
        />
      ),
      label: t("navigation.qrCode"),
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleMerchantClick = () => {
    dispatch(setCurrentPage("/merchant"));
    navigate("/merchant");
  };
  if (currentMenuKey === "/merchant") {
    return null;
  }
  return (
    <Sider
      theme="light"
      collapsed={sidebarCollapsed}
      onCollapse={(collapsed) => dispatch(setSidebarCollapsed(collapsed))}
      width={240}
      className={styles.sidebar}
    >
      <div className={styles.logoSection}>
        <img className={styles.logo} src={logo} alt="logo" />
      </div>

      <div className={styles.menuContainer}>
        <Menu
          className={styles.menu}
          theme="light"
          selectedKeys={[currentMenuKey]}
          mode="inline"
          items={menuItems}
          onClick={handleMenuClick}
        />
        <Button
          style={{ width: 184 }}
          type="primary"
          ghost
          onClick={handleMerchantClick}
        >
          商家管理
        </Button>
      </div>
    </Sider>
  );
};

export default Sidebar;
