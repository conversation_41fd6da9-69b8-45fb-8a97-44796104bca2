.svgIcon {
  display: inline-block;
  vertical-align: middle;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  // SVG元素样式
  :global(svg) {
    width: 100%;
    height: 100%;
    display: block;
  }

  // 悬停效果
  &:hover {
    transform: scale(1.1);
  }

  // 点击效果
  &:active {
    transform: scale(0.95);
  }
}

// 移除了加载状态相关样式，因为现在使用静态导入

// 错误状态
.error {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 2px;
  color: #ff4d4f;
  font-size: 12px;
  font-weight: bold;
}

// 禁用悬停效果的情况
.svgIcon.error {
  &:hover {
    transform: none;
  }

  &:active {
    transform: none;
  }

  cursor: default;
}
