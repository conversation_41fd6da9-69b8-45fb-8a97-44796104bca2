// 预览页面样式 - 根据Figma设计
.container {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  justify-content: center;
  padding-top: 94px;
  box-sizing: border-box;
}

.content {
  width: 343px;
  display: flex;
  flex-direction: column;
  gap: 56px;
}

.titleSection {
  margin: 0 auto;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .storeName {
    font-family: "PingFang SC", sans-serif;
    font-weight: 600;
    font-size: 24px;
    line-height: 32px;
    letter-spacing: 0.24px;
    color: #282828;
    margin: 0;
  }

  .shareTitle {
    font-family: "PingFang SC", sans-serif;
    font-weight: 600;
    font-size: 30px;
    line-height: 38px;
    letter-spacing: 0.3px;
    color: #282828;
    margin: 0;
  }
}

.socialList {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.socialItem {
  height: 62px;
  background: #ffffff;
  border: 1px solid #eeeeee;
  border-radius: 12px;
  box-shadow: 0px 2px 15px 0px rgba(0, 0, 0, 0.09);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;

  .socialInfo {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .socialIcon {
      width: 32px;
      height: 32px;
      flex-shrink: 0;
    }

    .socialDetails {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .socialAction {
        font-family: "PingFang SC", sans-serif;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        letter-spacing: 0.12px;
        color: #475467;
      }

      .socialName {
        font-family: "PingFang SC", sans-serif;
        font-weight: 500;
        font-size: 16px;
        line-height: 20px;
        letter-spacing: 0.16px;
        color: #202361;
      }
    }
  }

  .actionButton {
    width: 115px;
    height: 38px;
    background: #6186ee;
    border: none;
    border-radius: 12px;
    font-family: "PingFang SC", sans-serif;
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0.14px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #5272ca;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 60px 16px;
  }

  .content {
    width: 100%;
    max-width: 343px;
  }
}
