// 预览页面样式 - 根据Figma设计
.container {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  justify-content: center;
  padding-top: 94px;
  box-sizing: border-box;
}

.content {
  width: 343px;
  display: flex;
  flex-direction: column;
  gap: 56px;
}

.titleSection {
  margin: 0 auto;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 16px;

  .storeName {
    font-family: "PingFang SC", sans-serif;
    font-weight: 600;
    font-size: 24px;
    line-height: 32px;
    letter-spacing: 0.24px;
    color: #282828;
    margin: 0;
  }

  .shareTitle {
    font-family: "PingFang SC", sans-serif;
    font-weight: 600;
    font-size: 30px;
    line-height: 38px;
    letter-spacing: 0.3px;
    color: #282828;
    margin: 0;
  }
}

.socialList {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.socialItem {
  height: 62px;
  background: #ffffff;
  border: 1px solid #eeeeee;
  border-radius: 12px;
  box-shadow: 0px 2px 15px 0px rgba(0, 0, 0, 0.09);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;

  .socialInfo {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;

    .socialIcon {
      width: 32px;
      height: 32px;
      flex-shrink: 0;
    }

    .socialDetails {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .socialAction {
        font-family: "PingFang SC", sans-serif;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        letter-spacing: 0.12px;
        color: #475467;
      }

      .socialName {
        font-family: "PingFang SC", sans-serif;
        font-weight: 500;
        font-size: 16px;
        line-height: 20px;
        letter-spacing: 0.16px;
        color: #202361;
      }
    }
  }

  .actionButton {
    width: 115px;
    height: 38px;
    background: #6186ee;
    border: none;
    border-radius: 12px;
    font-family: "PingFang SC", sans-serif;
    font-weight: 600;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0.14px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #5272ca;
    }
  }
}

// 弹窗样式
.socialModal {
  :global(.ant-modal-content) {
    padding: 0;
    border-radius: 12px;
    overflow: hidden;
  }

  :global(.ant-modal-body) {
    padding: 0;
  }
}

.modalContent {
  position: relative;
  background: #ffffff;
  border-radius: 12px;
  height: 502px;
  overflow: hidden;
}

.closeButton {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 26px;
  height: 26px;
  border: none;
  background: transparent;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;

  &:hover {
    background: rgba(0, 0, 0, 0.1);
  }

  .closeIcon {
    margin-left: 2px;
    margin-top: 2px;
    width: 12px;
    height: 12px;
  }
}

.materialContainer {
  margin: 35px 16px 0 16px;
  display: flex;
  justify-content: center;

  .materialImage {
    width: 263px;
    height: 247px;
    border-radius: 8px;
    object-fit: cover;
  }
}

.copywritingContainer {
  margin: 19px 16px 0 16px;

  .copywritingText {
    font-family: "PingFang SC", sans-serif;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0.14px;
    color: #667085;
    margin: 0;
    word-wrap: break-word;
  }
}

.modalActions {
  position: absolute;
  bottom: 24px;
  left: 16px;
  right: 16px;
  display: flex;
  gap: 16px;

  .replaceButton {
    flex: 1;
    height: 44px;
    background: #ffffff;
    border: 1px solid #88a4f2;
    border-radius: 12px;
    font-family: "PingFang SC", sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0.16px;
    color: #5272ca;

    &:hover {
      border-color: #6186ee;
      color: #6186ee;
    }
  }

  .shareButton {
    flex: 1;
    height: 44px;
    background: #6186ee;
    border: none;
    border-radius: 12px;
    font-family: "PingFang SC", sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0.16px;
    color: #ffffff;

    &:hover {
      background: #5272ca;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .container {
    padding: 60px 16px;
  }

  .content {
    width: 100%;
    max-width: 343px;
  }
}
