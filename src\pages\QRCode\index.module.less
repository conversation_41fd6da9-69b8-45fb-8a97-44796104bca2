// 根据Figma设计的样式
.container {
  width: 100%;
  height: 100%;
  padding: 48px 64px 0 48px;
  box-sizing: border-box;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40px;

  .titleSection {
    flex: 1;

    .title {
      font-family: "PingFang SC", sans-serif;
      font-weight: 600;
      font-size: 24px;
      line-height: 32px;
      letter-spacing: 0.24px;
      color: #202361;
      margin: 0;
    }
  }

  .actionButtons {
    display: flex;
    gap: 24px;

    .previewButton {
      width: 181px;
      height: 48px;
      background: #6186ee;
      border: none;
      border-radius: 12px;
      font-family: "PingFang SC", sans-serif;
      font-weight: 600;
      font-size: 16px;
      line-height: 20px;
      letter-spacing: 0.16px;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;

      &:hover {
        background: #5272ca;
      }
    }

    .downloadButton {
      width: 181px;
      height: 48px;
      background: #ffffff;
      border: 1px solid #88a4f2;
      border-radius: 12px;
      font-family: "PingFang SC", sans-serif;
      font-weight: 600;
      font-size: 16px;
      line-height: 20px;
      letter-spacing: 0.16px;
      color: #5272ca;

      &:hover {
        border-color: #6186ee;
        color: #6186ee;
      }
    }
  }
}

.content {
  display: flex;
  gap: 24px;

  .leftPanel {
    width: 329px;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

.inputField {
  display: flex;
  flex-direction: column;
  gap: 6px;

  .label {
    font-family: "PingFang SC", sans-serif;
    font-weight: 500;
    font-size: 14px;
    line-height: 18px;
    letter-spacing: 0.14px;
    color: #202361;
  }

  .titleInput {
    height: 44px;
    background: #ffffff;
    border: 1px solid #d0d5dd;
    border-radius: 8px;
    padding: 12px;
    font-family: "PingFang SC", sans-serif;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0.16px;
    color: #202361;

    &:focus {
      border-color: #6186ee;
      box-shadow: 0 0 0 2px rgba(97, 134, 238, 0.1);
    }
  }
}

.qrCodeContainer {
  background: #f6f6f6;
  border-radius: 24px;
  padding: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  :global(.ant-qrcode) {
    border-radius: 8px;
  }
}

.shareSection {
  .shareInputContainer {
    display: flex;
    gap: 8px;
    align-items: flex-end;

    .shareInput {
      flex: 1;
      height: 44px;
      background: #ffffff;
      border: 1px solid #d0d5dd;
      border-radius: 8px;
      padding: 12px;
      font-family: "PingFang SC", sans-serif;
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      letter-spacing: 0.16px;
      color: #667085;

      &:focus {
        border-color: #6186ee;
        box-shadow: 0 0 0 2px rgba(97, 134, 238, 0.1);
      }
    }

    .copyButton {
      width: 36px;
      height: 36px;
      border: none;
      border-radius: 12px;
      background: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px;

      &:hover {
        background: rgba(97, 134, 238, 0.1);
      }

      .copyIcon {
        width: 24px;
        height: 24px;
      }
    }
  }
}
