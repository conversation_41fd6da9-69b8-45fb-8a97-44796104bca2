import React, { useMemo } from "react";
import styles from "./index.module.less";

// 导入所有SVG文件
import aiIconSvg from "../../assets/svgs/ai-icon.svg?raw";
import closeSvg from "../../assets/svgs/close.svg?raw";
import facebookSvg from "../../assets/svgs/facebook.svg?raw";
import fileSvg from "../../assets/svgs/file.svg?raw";
import homeSvg from "../../assets/svgs/home.svg?raw";
import imageSvg from "../../assets/svgs/image.svg?raw";
import qrCodeSvg from "../../assets/svgs/qr-code.svg?raw";
import tikTokSvg from "../../assets/svgs/tik-tok.svg?raw";
import whatsAppSvg from "../../assets/svgs/whats-app.svg?raw";
import xiaohongshuSvg from "../../assets/svgs/xiaohongshu.svg?raw";

// SVG映射表
const svgMap: Record<string, string> = {
  "ai-icon": aiIconSvg,
  close: closeSvg,
  facebook: facebookSvg,
  file: fileSvg,
  home: homeSvg,
  image: imageSvg,
  "qr-code": qrCodeSvg,
  "tik-tok": tikTokSvg,
  "whats-app": whatsAppSvg,
  xiaohongshu: xiaohongshuSvg,
};

interface SvgIconProps {
  /** SVG文件名（不包含.svg扩展名） */
  name: string;
  /** 图标大小，默认为24px */
  size?: number | string;
  /** 图标颜色，支持任何CSS颜色值 */
  color?: string;
  /** 自定义类名 */
  className?: string;
  /** 点击事件 */
  onClick?: () => void;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

const SvgIcon: React.FC<SvgIconProps> = ({
  name,
  size = 24,
  color,
  className,
  onClick,
  style,
}) => {
  const svgContent = useMemo(() => {
    const originalSvg = svgMap[name];
    if (!originalSvg) {
      return null;
    }

    // 如果指定了颜色，替换SVG中的fill和stroke属性
    if (color) {
      let modifiedSvg = originalSvg;
      // 替换所有的fill属性，但保留fill="none"，保留颜色 #FF805A
      modifiedSvg = modifiedSvg.replace(/fill="(?!none)[^"]*"/g, (match) => {
        if (match === 'fill="#FF805A"') {
          return match;
        }
        return `fill="${color}"`;
      });
      // 替换stroke属性（如果SVG使用stroke而不是fill），保留颜色 #FF805A
      modifiedSvg = modifiedSvg.replace(/stroke="(?!none)[^"]*"/g, (match) => {
        if (match === 'stroke="#FF805A"') {
          return match;
        }
        return `stroke="${color}"`;
      });
      return modifiedSvg;
    }

    return originalSvg;
  }, [name, color]);

  const iconStyle: React.CSSProperties = {
    width: typeof size === "number" ? `${size}px` : size,
    height: typeof size === "number" ? `${size}px` : size,
    display: "inline-block",
    verticalAlign: "middle",
    ...style,
  };

  // 如果SVG不存在，显示错误状态
  if (!svgContent) {
    return (
      <span
        className={`${styles.svgIcon} ${styles.error} ${className || ""}`}
        style={iconStyle}
        title={`Icon not found: ${name}`}
      >
        {/* 错误状态的占位符 */}?
      </span>
    );
  }

  return (
    <span
      className={`${styles.svgIcon} ${className || ""}`}
      style={iconStyle}
      onClick={onClick}
      dangerouslySetInnerHTML={{ __html: svgContent }}
    />
  );
};

export default SvgIcon;
