import React, { useEffect, useState } from "react";
import { But<PERSON>, Modal } from "antd";
import styles from "./index.module.less";
import closeSvg from "@/assets/svgs/close.svg";
import facebookSvg from "@/assets/svgs/facebook.svg";
import tiktokSvg from "@/assets/svgs/tik-tok.svg";
import whatsappSvg from "@/assets/svgs/whats-app.svg";
import xiaohongshuSvg from "@/assets/svgs/xiaohongshu.svg";

// 社交媒体图标资源
const facebookIcon = facebookSvg;
const tiktokIcon = tiktokSvg;
const whatsappIcon = whatsappSvg;
const xiaohongshuIcon = xiaohongshuSvg;

// 弹窗资源
const closeIcon = closeSvg;

// 模拟素材数据
const mockMaterials = [
  "http://localhost:3845/assets/48370cc2d604bba61141e1a8de0ef5aaeaa70664.png",
  "https://picsum.photos/263/247?random=1",
  "https://picsum.photos/263/247?random=2",
  "https://picsum.photos/263/247?random=3",
  "https://picsum.photos/263/247?random=4",
];

// 模拟文案数据
const mockCopywriting = [
  "🍽️ 今天来到这家宝藏餐厅，每一道菜都让人惊艳！特别推荐他们的招牌菜，口感层次丰富，绝对值得一试！服务态度也超级棒，环境优雅舒适，是和朋友聚餐的绝佳选择！💫 #美食推荐 #餐厅打卡 #美味体验",
  "✨ 发现了一家超棒的咖啡店！环境温馨，咖啡香浓，甜品也很赞！工作学习的好去处，强烈推荐给大家！☕️ #咖啡时光 #生活美学",
  "🎉 今天的购物收获满满！这家店的服务真的太贴心了，产品质量也很棒，价格还很实惠！已经成为我的心头好了！💕 #购物分享 #好物推荐",
  "🌟 周末和朋友们一起来这里玩，氛围超级棒！活动丰富多彩，工作人员也很热情，度过了愉快的一天！🎈 #周末时光 #朋友聚会",
  "💎 这次的体验真的超出预期！从服务到产品都让人印象深刻，细节处理得很到位，值得五星好评！👍 #体验分享 #五星推荐",
];

interface SocialMediaItem {
  id: string;
  name: string;
  icon: string;
  action: string;
  buttonText: string;
}

interface SocialMediaConfig {
  material: string;
  copywriting: string;
}

const QRPreview: React.FC = () => {
  const [title, setTitle] = useState("分享并解锁奖励");
  const [storeName, setStoreName] = useState("[商店名字]");
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedSocialMedia, setSelectedSocialMedia] =
    useState<SocialMediaItem | null>(null);
  const [currentConfig, setCurrentConfig] = useState<SocialMediaConfig>({
    material: "",
    copywriting: "",
  });

  // 随机选择素材和文案
  const getRandomConfig = (): SocialMediaConfig => {
    const randomMaterial =
      mockMaterials[Math.floor(Math.random() * mockMaterials.length)];
    const randomCopywriting =
      mockCopywriting[Math.floor(Math.random() * mockCopywriting.length)];
    return {
      material: randomMaterial,
      copywriting: randomCopywriting,
    };
  };

  // 社交媒体列表
  const socialMediaList: SocialMediaItem[] = [
    {
      id: "facebook",
      name: "Facebook",
      icon: facebookIcon,
      action: "关注",
      buttonText: "关注",
    },
    {
      id: "facebook2",
      name: "Facebook",
      icon: facebookIcon,
      action: "评论",
      buttonText: "评论",
    },
    {
      id: "tiktok1",
      name: "TikToK",
      icon: tiktokIcon,
      action: "关注",
      buttonText: "关注",
    },
    {
      id: "tiktok2",
      name: "TikToK",
      icon: tiktokIcon,
      action: "评论",
      buttonText: "评论",
    },
    {
      id: "xiaohongshu",
      name: "小红书",
      icon: xiaohongshuIcon,
      action: "关注",
      buttonText: "关注",
    },
    {
      id: "xiaohongshu2",
      name: "小红书",
      icon: xiaohongshuIcon,
      action: "评论",
      buttonText: "评论",
    },
    {
      id: "whatsapp",
      name: "WhatsApp",
      icon: whatsappIcon,
      action: "社群",
      buttonText: "加入",
    },
  ];

  useEffect(() => {
    // 从URL参数获取标题和商店名
    const urlParams = new URLSearchParams(window.location.search);
    const titleParam = urlParams.get("title");
    const storeParam = urlParams.get("store");

    if (titleParam) {
      setTitle(decodeURIComponent(titleParam));
    }
    if (storeParam) {
      setStoreName(decodeURIComponent(storeParam));
    }
  }, []);

  const handleSocialAction = (item: SocialMediaItem) => {
    // 打开弹窗并随机选择配置
    setSelectedSocialMedia(item);
    setCurrentConfig(getRandomConfig());
    setModalVisible(true);
  };

  // 替换配置
  const handleReplaceConfig = () => {
    setCurrentConfig(getRandomConfig());
  };

  // 分享配置
  const handleShareConfig = () => {
    // 这里可以实现具体的分享逻辑
    console.log(`分享${selectedSocialMedia?.name}配置:`, currentConfig);
    setModalVisible(false);
  };

  // 关闭弹窗
  const handleCloseModal = () => {
    setModalVisible(false);
    setSelectedSocialMedia(null);
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {/* 标题区域 */}
        <div className={styles.titleSection}>
          <h1 className={styles.storeName}>{storeName}</h1>
          <h2 className={styles.shareTitle}>{title}</h2>
        </div>

        {/* 社交媒体列表 */}
        <div className={styles.socialList}>
          {socialMediaList.map((item) => (
            <div key={item.id} className={styles.socialItem}>
              <div className={styles.socialInfo}>
                <img
                  src={item.icon}
                  alt={item.name}
                  className={styles.socialIcon}
                />
                <div className={styles.socialDetails}>
                  <div className={styles.socialAction}>{item.action}</div>
                  <div className={styles.socialName}>{item.name}</div>
                </div>
              </div>
              <Button
                type="primary"
                className={styles.actionButton}
                onClick={() => handleSocialAction(item)}
              >
                {item.buttonText}
              </Button>
            </div>
          ))}
        </div>
      </div>

      {/* 社交媒体配置弹窗 */}
      <Modal
        open={modalVisible}
        onCancel={handleCloseModal}
        footer={null}
        closable={false}
        centered
        width={343}
        className={styles.socialModal}
        styles={{ mask: { backgroundColor: "rgba(0, 0, 0, 0.25)" } }}
      >
        <div className={styles.modalContent}>
          {/* 关闭按钮 */}
          <button className={styles.closeButton} onClick={handleCloseModal}>
            <img src={closeIcon} alt="close" className={styles.closeIcon} />
          </button>

          {/* 素材图片 */}
          <div className={styles.materialContainer}>
            <img
              src={currentConfig.material}
              alt="material"
              className={styles.materialImage}
            />
          </div>

          {/* 文案内容 */}
          <div className={styles.copywritingContainer}>
            <p className={styles.copywritingText}>
              {currentConfig.copywriting}
            </p>
          </div>

          {/* 操作按钮 */}
          <div className={styles.modalActions}>
            <Button
              className={styles.replaceButton}
              onClick={handleReplaceConfig}
            >
              替换
            </Button>
            <Button
              type="primary"
              className={styles.shareButton}
              onClick={handleShareConfig}
            >
              分享
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default QRPreview;
