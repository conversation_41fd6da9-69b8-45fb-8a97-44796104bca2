import React, { useEffect, useState } from 'react';
import { Button } from 'antd';
import styles from './index.module.less';

// 社交媒体图标资源
const facebookIcon = "http://localhost:3845/assets/2019e8e00551463383bb27c0b77dd1ceaf161582.svg";
const tiktokIcon = "http://localhost:3845/assets/72c48827e037056e8a1fb0d7279688ebdf71eeef.svg";
const whatsappIcon = "http://localhost:3845/assets/78d1450f760131394c33256cde41770f9bdd0827.svg";

interface SocialMediaItem {
  id: string;
  name: string;
  icon: string;
  action: string;
  buttonText: string;
}

const QRPreview: React.FC = () => {
  const [title, setTitle] = useState('分享并解锁奖励');
  const [storeName, setStoreName] = useState('[商店名字]');

  // 社交媒体列表
  const socialMediaList: SocialMediaItem[] = [
    {
      id: 'facebook',
      name: 'Facebook',
      icon: facebookIcon,
      action: '关注',
      buttonText: '关注'
    },
    {
      id: 'tiktok1',
      name: 'TikToK',
      icon: tiktokIcon,
      action: '关注',
      buttonText: '关注'
    },
    {
      id: 'tiktok2',
      name: 'TikToK',
      icon: tiktokIcon,
      action: '评论',
      buttonText: '评论'
    },
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: whatsappIcon,
      action: '社群',
      buttonText: '加入'
    }
  ];

  useEffect(() => {
    // 从URL参数获取标题和商店名
    const urlParams = new URLSearchParams(window.location.search);
    const titleParam = urlParams.get('title');
    const storeParam = urlParams.get('store');
    
    if (titleParam) {
      setTitle(decodeURIComponent(titleParam));
    }
    if (storeParam) {
      setStoreName(decodeURIComponent(storeParam));
    }
  }, []);

  const handleSocialAction = (item: SocialMediaItem) => {
    // 这里可以实现具体的社交媒体操作
    console.log(`执行${item.name}的${item.action}操作`);
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {/* 标题区域 */}
        <div className={styles.titleSection}>
          <h1 className={styles.storeName}>{storeName}</h1>
          <h2 className={styles.shareTitle}>{title}</h2>
        </div>

        {/* 社交媒体列表 */}
        <div className={styles.socialList}>
          {socialMediaList.map((item) => (
            <div key={item.id} className={styles.socialItem}>
              <div className={styles.socialInfo}>
                <img src={item.icon} alt={item.name} className={styles.socialIcon} />
                <div className={styles.socialDetails}>
                  <div className={styles.socialAction}>{item.action}</div>
                  <div className={styles.socialName}>{item.name}</div>
                </div>
              </div>
              <Button
                type="primary"
                className={styles.actionButton}
                onClick={() => handleSocialAction(item)}
              >
                {item.buttonText}
              </Button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default QRPreview;
