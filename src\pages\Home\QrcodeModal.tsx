import React from "react";
import { Modal, Typography } from "antd";
import styles from "./QrcodeModal.module.less";
import SvgIcon from "@/components/SvgIcon";
import { QRCode as AntdQRCode } from "antd";

const { Title } = Typography;

// 二维码弹窗组件属性接口
interface QrcodeModalProps {
  visible: boolean;
  onClose: () => void;
}

const QrcodeModal: React.FC<QrcodeModalProps> = ({ visible, onClose }) => {
  const shareUrl = "https://example.com/share";

  // 社交媒体平台图标
  const socialIcons = [
    {
      name: "facebook",
    },
    {
      name: "x<PERSON><PERSON><PERSON><PERSON>",
    },
    {
      name: "tik-tok",
    },
    {
      name: "whats-app",
    },
  ];

  return (
    <Modal
      title="二维码"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={578}
      centered
      className={styles.qrcodeModal}
      closeIcon={<SvgIcon name="close" size={14} />}
    >
      <div className={styles.qrcodeContainer}>
        <div className={styles.qrcodeContent}>
          <Title level={3} className={styles.qrcodeTitle}>
            分享并解锁奖励
          </Title>

          <div className={styles.qrcodeImage}>
            <AntdQRCode
              value={shareUrl}
              size={265}
              style={{ backgroundColor: "#f6f6f6", border: 0 }}
            />
          </div>

          <div className={styles.socialIcons}>
            {socialIcons.map((platform) => (
              <div key={platform.name} className={styles.socialIconItem}>
                <SvgIcon name={platform.name.toLowerCase()} size={24} />
              </div>
            ))}
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default QrcodeModal;
