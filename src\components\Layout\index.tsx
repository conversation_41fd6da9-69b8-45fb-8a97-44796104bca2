import React from "react";
import { Layout } from "antd";
import { Outlet } from "react-router-dom";
import Header from "../Header";
import Sidebar from "../Sidebar";
import { useAppSelector } from "@/store/hooks";
import styles from "./index.module.less";

const { Content } = Layout;

const MainLayout: React.FC = () => {
  const { sidebarCollapsed, currentPage } = useAppSelector(
    (state) => state.app
  );

  return (
    <Layout className={styles.layout}>
      <Sidebar />
      <Layout
        className={styles.contentLayout}
        style={{
          marginLeft:
            currentPage === "/merchant" ? 0 : sidebarCollapsed ? 80 : 240,
        }}
      >
        <Header />
        <Content className={styles.content}>
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
