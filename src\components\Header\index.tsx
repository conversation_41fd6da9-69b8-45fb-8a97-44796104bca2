import React from "react";
import { Layout, Dropdown, Avatar, Modal, message } from "antd";
import {
  DownOutlined,
  UserOutlined,
  GlobalOutlined,
  LogoutOutlined,
} from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { clearToken } from "@/services/authService";
import { useTranslation } from "react-i18next";
import type { MenuProps } from "antd";
import styles from "./index.module.less";
import logo from "@/assets/images/logo.png";
import { setCurrentPage } from "@/store/slices/appSlice";
import { useAppDispatch } from "@/store/hooks";

const { Header: AntHeader } = Layout;

const Header: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const pathName = location.pathname.split("/")?.[1] || "";

  // 处理退出登录
  const handleLogout = () => {
    Modal.confirm({
      title: "确认退出",
      content: "您确定要退出登录吗？",
      okText: "确认",
      cancelText: "取消",
      onOk: () => {
        // 清除token
        clearToken();

        // 提示用户
        message.success("已成功退出登录");

        // 跳转到登录页
        navigate("/login");
      },
    });
  };

  const languageItems: MenuProps["items"] = [
    {
      key: "zh",
      label: "中文",
      icon: <GlobalOutlined />,
    },
  ];

  const userItems: MenuProps["items"] = [
    {
      key: "profile",
      label: t("common.profile"),
      icon: <UserOutlined />,
    },
    {
      type: "divider",
    },
    {
      key: "logout",
      label: "退出登录",
      icon: <LogoutOutlined />,
      onClick: () => handleLogout(),
    },
  ];

  const handleLogoClick = () => {
    dispatch(setCurrentPage("/"));
    navigate("/");
  };

  return (
    <AntHeader className={styles.header}>
      <div className={styles.leftSection}>
        {pathName !== "merchant" ? (
          <h1 className={styles.storeName}>{t("common.storeName")}</h1>
        ) : (
          <a onClick={handleLogoClick}>
            <img className={styles.logo} src={logo} alt="logo" />
          </a>
        )}
      </div>

      <div className={styles.rightSection}>
        <Dropdown menu={{ items: languageItems }} placement="bottomRight">
          <div className={styles.languageSelector}>
            {t("common.language")}
            <DownOutlined />
          </div>
        </Dropdown>

        <Dropdown menu={{ items: userItems }} placement="bottomRight">
          <Avatar icon={<UserOutlined />} className={styles.userAvatar} />
        </Dropdown>
      </div>
    </AntHeader>
  );
};

export default Header;
