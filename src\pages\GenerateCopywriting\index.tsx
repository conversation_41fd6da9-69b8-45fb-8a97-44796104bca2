import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Form, Input, Select, Button, Spin, message, Modal } from "antd";
import { EditOutlined, DeleteOutlined } from "@ant-design/icons";
import {
  getCopywriting,
  createCopywriting,
  updateCopywriting,
} from "@/services/copywritingService";
import aiCopywritingOptionsAdd from "@/assets/images/ai-copywriting-options-add.png";
import styles from "./index.module.less";

const { TextArea } = Input;

// 表单值类型定义
interface FormValues {
  title: string;
  aiModel: string;
  count: number;
  language: string;
  prompt: string;
}

const GenerateCopywriting: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm<FormValues>();

  // 状态
  const [loading, setLoading] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [generatedContents, setGeneratedContents] = useState<string[]>([]);
  const [editContent, setEditContent] = useState("");
  const [editIndex, setEditIndex] = useState(0);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [titleOptions, setTitleOptions] = useState<
    { value: string; label: string }[]
  >([
    { value: "美食推荐文案", label: "美食推荐文案" },
    { value: "促销活动", label: "促销活动" },
    { value: "新品发布", label: "新品发布" },
    { value: "节日祝福", label: "节日祝福" },
  ]);
  const [newTitle, setNewTitle] = useState("");
  const [addTitleModalVisible, setAddTitleModalVisible] = useState(false);

  // 判断是否为编辑模式
  const isEditMode = !!id;

  // 选项数据
  const aiModelOptions = [
    { value: "DeepSeek", label: "DeepSeek" },
    { value: "GPT-4", label: "GPT-4" },
    { value: "GPT-3.5", label: "GPT-3.5" },
    { value: "Claude", label: "Claude" },
  ];

  const countOptions = [
    { value: 1, label: "1" },
    { value: 3, label: "3" },
    { value: 5, label: "5" },
    { value: 10, label: "10" },
  ];

  const languageOptions = [
    { value: "zh", label: "中文" },
    { value: "en", label: "英文" },
  ];

  // 加载文案数据
  useEffect(() => {
    // 如果是编辑模式，加载文案数据
    if (id) {
      setLoading(true);
      getCopywriting(id)
        .then((data) => {
          if (data) {
            // 设置表单数据
            form.setFieldsValue({
              title: data.title,
              aiModel: data.aiModel || "DeepSeek",
              count: data.count || 1,
              language: data.language || "zh",
              prompt: data.prompt || "",
            });
            if (data.contents && data.contents.length > 0) {
              setGeneratedContents(data.contents);
            }
          }
        })
        .catch((err) => {
          message.error("加载文案失败: " + err.message);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [id, form]);

  // 处理生成文案
  const handleGenerate = () => {
    form.validateFields().then((values) => {
      setGenerating(true);

      // 模拟AI生成文案
      setTimeout(() => {
        // 根据选择的数量生成多个文案
        const mockContents = [];
        const count = values.count || 1;

        for (let i = 0; i < count; i++) {
          mockContents.push(
            `这是由${values.aiModel}生成的第${i + 1}个${
              values.language === "zh" ? "中文" : "英文"
            }文案。\n\n基于提示词: ${
              values.prompt
            }\n\n这是第一段示例内容，展示了AI生成的文案效果。\n\n这是第二段示例内容，继续围绕主题展开。`
          );
        }

        setGeneratedContents(mockContents);
        setGenerating(false);
      }, 2000);
    });
  };

  // 处理保存文案
  const handleSave = () => {
    form.validateFields().then((values) => {
      const saveData = {
        title: values.title,
        aiModel: values.aiModel,
        count: values.count,
        language: values.language,
        prompt: values.prompt,
        contents: generatedContents,
      };

      setLoading(true);

      // 根据是否为编辑模式选择创建或更新操作
      const savePromise = isEditMode
        ? updateCopywriting(id as string, saveData)
        : createCopywriting(saveData);

      savePromise
        .then(() => {
          message.success(isEditMode ? "文案更新成功" : "文案保存成功");
          navigate("/copywriting");
        })
        .catch((err) => {
          message.error("保存失败: " + err.message);
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  // 处理编辑文案内容
  const handleEditContent = (content: string, index: number) => {
    setEditContent(content);
    setEditIndex(index);
    setEditModalVisible(true);
  };

  // 保存编辑后的文案内容
  const handleSaveEdit = () => {
    const newContents = [...generatedContents];
    newContents[editIndex] = editContent;
    setGeneratedContents(newContents);
    setEditModalVisible(false);
  };

  // 保存新标题
  const handleSaveNewTitle = () => {
    if (newTitle.trim()) {
      setTitleOptions((prev) => [
        ...prev,
        { value: newTitle, label: newTitle },
      ]);
      form.setFieldsValue({
        title: newTitle,
      });
      setNewTitle("");
      setAddTitleModalVisible(false);
    }
  };

  // 复制文案内容
  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content);
    message.success("文案已复制到剪贴板");
  };

  const deleteContent = (index: number) => {
    const newContents = [...generatedContents];
    newContents.splice(index, 1);
    setGeneratedContents(newContents);
  };

  // 渲染UI
  return (
    <div className={styles.generateContainer}>
      <Spin spinning={loading}>
        <div className={styles.header}>
          <h1 className={styles.pageTitle}>AI 文案生成</h1>
          <p className={styles.pageDesc}>
            使用 AI 智能生成高质量的社交媒体文案
          </p>
        </div>

        <div className={styles.content}>
          <div className={styles.formSection}>
            <Form
              form={form}
              layout="vertical"
              initialValues={{
                aiModel: "DeepSeek",
                count: 3,
                language: "zh",
                prompt: "",
              }}
            >
              <div className={styles.formGrid}>
                <div className={styles.formCol}>
                  <Form.Item
                    name="title"
                    label="文案名称"
                    className={styles.formItem}
                    rules={[{ required: true, message: "请选择或添加新名称" }]}
                  >
                    <Select
                      size="large"
                      placeholder="例如：美食推荐文案"
                      options={titleOptions}
                      showSearch
                      allowClear
                      className={styles.selectWithArrow}
                      popupRender={(menu) => (
                        <>
                          <div
                            className={styles.addTitleButton}
                            onClick={() => setAddTitleModalVisible(true)}
                          >
                            <img src={aiCopywritingOptionsAdd} alt="" />
                            <span>添加新名称</span>
                          </div>
                          {menu}
                        </>
                      )}
                    />
                  </Form.Item>

                  <Form.Item
                    name="aiModel"
                    label="AI 模型"
                    className={styles.formItem}
                    rules={[{ required: true, message: "请选择AI模型" }]}
                  >
                    <Select
                      size="large"
                      placeholder="选择AI模型"
                      options={aiModelOptions}
                      className={styles.selectWithArrow}
                    />
                  </Form.Item>

                  <Form.Item
                    name="count"
                    label="生成数量"
                    className={styles.formItem}
                    rules={[{ required: true, message: "请选择生成数量" }]}
                  >
                    <Select
                      size="large"
                      placeholder="选择生成数量"
                      options={countOptions}
                      className={styles.selectWithArrow}
                    />
                  </Form.Item>

                  <Form.Item
                    name="language"
                    label="文案语言"
                    className={styles.formItem}
                    rules={[{ required: true, message: "请选择语言" }]}
                  >
                    <Select
                      size="large"
                      placeholder="选择语言"
                      options={languageOptions}
                      className={styles.selectWithArrow}
                    />
                  </Form.Item>
                </div>

                <div className={styles.formCol}>
                  <Form.Item
                    name="prompt"
                    label="提示词"
                    className={styles.formItem}
                    rules={[{ required: true, message: "请输入提示词" }]}
                  >
                    <TextArea
                      placeholder="为一家高端餐厅写一段吸引人的推荐文案，突出菜品质量和用餐体验，请调整热情友好..."
                      rows={10}
                      className={styles.promptTextarea}
                    />
                  </Form.Item>

                  <Form.Item className={styles.generateBtnItem}>
                    <Button
                      className={styles.generateBtn}
                      onClick={handleGenerate}
                      loading={generating}
                    >
                      开始生成
                    </Button>
                  </Form.Item>
                </div>
              </div>
            </Form>
          </div>

          {generatedContents.length > 0 && (
            <div className={styles.resultSection}>
              <div className={styles.resultHeader}>
                <h2 className={styles.resultTitle}>生成结果</h2>
              </div>
              <div className={styles.resultList}>
                {generatedContents.map((content, idx) => (
                  <div key={idx} className={styles.resultCard}>
                    <div className={styles.resultContent}>
                      {content.split("\n\n").map((paragraph, index) => (
                        <p key={index} className={styles.resultParagraph}>
                          {paragraph}
                        </p>
                      ))}
                    </div>
                    <div className={styles.resultActions}>
                      <Button
                        className={styles.actionBtn}
                        type="text"
                        onClick={(e) => {
                          e.preventDefault();
                          handleEditContent(content, idx);
                        }}
                      >
                        <EditOutlined className={styles.actionIcon} />
                      </Button>
                      <Button
                        className={styles.actionBtn}
                        type="text"
                        danger
                        onClick={(e) => {
                          e.preventDefault();
                          deleteContent(idx);
                        }}
                      >
                        <DeleteOutlined className={styles.actionIcon} />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </Spin>

      {/* 编辑文案内容对话框 */}
      <Modal
        title="编辑文案内容"
        open={editModalVisible}
        onOk={handleSaveEdit}
        onCancel={() => setEditModalVisible(false)}
        width={700}
        className={styles.editModal}
      >
        <Input.TextArea
          value={editContent}
          onChange={(e) => setEditContent(e.target.value)}
          rows={12}
          placeholder="输入文案内容"
          className={styles.editTextarea}
        />
      </Modal>

      {/* 添加新标题对话框 */}
      <Modal
        width={450}
        title="添加新名称"
        open={addTitleModalVisible}
        onOk={handleSaveNewTitle}
        onCancel={() => setAddTitleModalVisible(false)}
        className={`${styles.titleModal} center-footer`}
        zIndex={9999}
      >
        <Input
          value={newTitle}
          onChange={(e) => setNewTitle(e.target.value)}
          placeholder="输入新文案名称"
          className={styles.titleInput}
        />
      </Modal>
    </div>
  );
};

export default GenerateCopywriting;
