.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 48px !important;
  background: var(--card-background);
  border-bottom: 1px solid #e4e7ec;
  box-shadow: var(--shadow-sm);
  height: 102px !important;
}

.leftSection {
  display: flex;
  align-items: center;
  gap: 16px;
}

.storeName {
  margin: 0;
  font-weight: 600;
  font-size: 30px;
  line-height: 38px;
  color: var(--text-primary);
}
.logo {
  width: 78px;
  height: 78px;
}

.rightSection {
  display: flex;
  align-items: center;
  gap: 24px;
}

.languageSelector {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--text-secondary);
  font-size: 14px;

  &:hover {
    color: var(--primary-color);
  }
}

.userAvatar {
  cursor: pointer;

  &:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
  }
}
