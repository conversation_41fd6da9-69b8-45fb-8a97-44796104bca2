.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #0d0d1a;
  z-index: -1;
}

.wave-lines {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 1000 1000"><path d="M0,500 Q250,400 500,500 T1000,500" stroke="rgba(255,255,255,0.1)" fill="none" stroke-width="1" /></svg>');
  background-size: cover;
  opacity: 0.5;
}

.login-card {
  width: 504px;
  height: 512px;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  background-color: #fff;
  backdrop-filter: blur(10px);
}

.login-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
  img {
    width: 78px;
    height: 78px;
  }
}

.logo-placeholder {
  width: 120px;
  height: 40px;
  background-color: #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  color: #666;
  border-radius: 4px;
}

.slider-captcha {
  width: 100%;
  height: 40px;
  margin: 16px 0;
}

.slider-track {
  position: relative;
  width: 100%;
  height: 40px;
  background-color: #e8e8e8;
  border-radius: 20px;
  overflow: hidden;
}

.slider-handler {
  position: absolute;
  left: 0;
  top: 0;
  width: 40px;
  height: 40px;
  background-color: #1677ff;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 50%;
  z-index: 1;
  transition: left 0.3s;
  user-select: none;
}

.slider-text {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #999;
  font-size: 14px;
  user-select: none;
}

.login-button {
  height: 48px;
  font-size: 16px;
  background-color: #1677ff;
  border: none;
  margin-top: 8px;
}

/* 添加滑块验证的交互效果 */
@keyframes success-animation {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.slider-success .slider-handler {
  animation: success-animation 0.5s ease;
  background-color: #52c41a;
}

/* 响应式调整 */
@media (max-width: 576px) {
  .login-card {
    width: 90%;
    max-width: 400px;
  }
}
