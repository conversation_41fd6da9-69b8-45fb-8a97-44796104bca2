// 二维码弹窗样式
.qrcodeModal {
  :global {
    .ant-modal-content {
      border-radius: 16px;
      overflow: hidden;
    }

    .ant-modal-header {
      padding: 16px 24px;
      border-bottom: none;

      .ant-modal-title {
        font-family: "PingFang SC", sans-serif;
        font-weight: 600;
        font-size: 18px;
        color: #202361;
      }
    }

    .ant-modal-body {
      padding: 0;
    }

    .ant-modal-close {
      top: 16px;
      right: 16px;
    }
  }
}

.closeIcon {
  font-size: 20px;
  color: #202361;
}

// 二维码内容容器
.qrcodeContainer {
  width: 432px;
  height: 568px;
  padding: 0;
  margin: 0 auto;
}

// 二维码内容
.qrcodeContent {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-image: url("@/assets/images/qr-code-bg.png");
  background-size: cover;
  color: #ffffff;
  border-radius: 20px;
}

// 二维码标题
.qrcodeTitle {
  font-family: "PingFang SC", sans-serif;
  font-weight: 600;
  font-size: 24px !important;
  color: #ffffff !important;
  margin-bottom: 24px !important;
  text-align: center;
}

// 二维码图片容器
.qrcodeImage {
  background-color: #f6f6f6;
  border-radius: 12px;
  padding: 16px;
  width: 329px;
  height: 329px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// 社交图标容器
.socialIcons {
  margin-top: 35px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

// 单个社交图标
.socialIconItem {
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }

  img {
    width: 24px;
    height: 24px;
    object-fit: contain;
  }
}
