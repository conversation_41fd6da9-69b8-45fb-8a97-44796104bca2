{"name": "social-sharing-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@reduxjs/toolkit": "^2.8.2", "@types/qs": "^6.14.0", "@types/react-router-dom": "^5.3.3", "antd": "^5.12.8", "axios": "^1.10.0", "i18next": "^23.7.8", "ogl": "^1.0.11", "qs": "^6.14.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^13.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "less": "^4.2.0", "typescript": "^5.2.2", "vite": "^5.0.8"}}