.sidebar {
  overflow: auto;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  background: #f9fafb !important;
  border: none !important;
  :global {
    .ant-layout-sider-trigger {
      background: #f9fafb !important;
    }
    .ant-layout-sider-children {
      display: flex;
      flex-direction: column;
    }
  }
}
.menu {
}

.logoSection {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  margin-top: 24px;
  .logo {
    width: 78px;
    height: 78px;
  }
}

.logoText {
  color: var(--primary-color);
  font-weight: 600;
  font-size: 16px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.menuContainer {
  flex: 1;
  padding-bottom: 24px;
  margin-top: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  :global(.ant-menu) {
    background: transparent;

    border: none !important;
  }

  :global(.ant-menu-item) {
    border-radius: 8px;
    margin: 4px 8px;
    width: calc(100% - 16px);

    &:hover {
      background-color: #f1f5f9;
    }

    &.ant-menu-item-selected {
      background-color: var(--primary-color);
      color: white;

      .ant-menu-item-icon {
        color: white;
      }
    }
  }

  :global(.ant-menu-item-icon) {
    font-size: 16px;
  }
}
