// React已经在JSX转换中自动导入，不需要显式导入
import { createBrowserRouter, Navigate, Outlet } from "react-router-dom";
import MainLayout from "@/components/Layout";
import Home from "@/pages/Home";
import Materials from "@/pages/Materials";
import Copywriting from "@/pages/Copywriting";
import GenerateCopywriting from "@/pages/GenerateCopywriting";
import QRCode from "@/pages/QRCode";
import QRPreview from "@/pages/QRPreview";
import Login from "@/pages/Login";
import { isLoggedIn } from "@/services/authService";
import Merchant from "@/pages/Merchant";

// 路由守卫组件，用于保护需要登录才能访问的路由
const ProtectedRoute = () => {
  if (!isLoggedIn()) {
    return <Navigate to="/login" replace />;
  }
  return <Outlet />;
};

export const router = createBrowserRouter([
  {
    path: "/",
    element: <ProtectedRoute />,
    children: [
      {
        element: <MainLayout />,
        children: [
          {
            index: true,
            element: <Home />,
          },
          {
            path: "materials",
            element: <Materials />,
          },
          {
            path: "copywriting",
            element: <Copywriting />,
          },
          {
            path: "ai-copywriting/:id?",
            element: <GenerateCopywriting />,
          },
          {
            path: "qrcode",
            element: <QRCode />,
          },
          {
            path: "merchant",
            element: <Merchant />,
          },
        ],
      },
    ],
  },
  {
    path: "/login",
    element: <Login />,
  },
  {
    path: "/qr-preview",
    element: <QRPreview />,
  },
  {
    path: "*",
    element: <Navigate to="/" replace />,
  },
]);

export default router;
