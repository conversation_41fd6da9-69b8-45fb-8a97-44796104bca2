# SvgIcon 组件

一个通用的SVG图标组件，支持加载 `src/assets/svgs` 文件夹中的SVG文件，并支持动态修改颜色。

## 功能特性

- ✅ 自动加载 `src/assets/svgs` 文件夹中的SVG文件
- ✅ 支持动态修改SVG颜色（fill和stroke属性）
- ✅ 支持自定义大小
- ✅ 支持点击事件
- ✅ 支持自定义样式和类名
- ✅ TypeScript支持
- ✅ 错误处理和提示

## 使用方法

### 基础用法

```tsx
import SvgIcon from '@/components/SvgIcon';

// 基础使用
<SvgIcon name="home" />

// 自定义大小和颜色
<SvgIcon name="home" size={24} color="#1890ff" />

// 添加点击事件
<SvgIcon 
  name="close" 
  size={16} 
  color="#ff4d4f" 
  onClick={() => console.log('clicked')} 
/>
```

### 在菜单中使用

```tsx
const menuItems = [
  {
    key: "/",
    icon: <SvgIcon name="home" size={16} color="#667085" />,
    label: "首页",
  },
  {
    key: "/materials",
    icon: <SvgIcon name="image" size={16} color="#667085" />,
    label: "素材管理",
  },
];
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| name | string | - | SVG文件名（不包含.svg扩展名） |
| size | number \| string | 24 | 图标大小，数字时单位为px |
| color | string | - | 图标颜色，支持任何CSS颜色值 |
| className | string | - | 自定义类名 |
| onClick | () => void | - | 点击事件 |
| style | React.CSSProperties | - | 自定义样式 |

## 可用图标

当前支持的图标列表：

- `ai-icon` - AI图标
- `close` - 关闭图标
- `facebook` - Facebook图标
- `file` - 文件图标
- `home` - 首页图标
- `image` - 图片图标
- `qr-code` - 二维码图标
- `tik-tok` - TikTok图标
- `whats-app` - WhatsApp图标
- `xiaohongshu` - 小红书图标

## 添加新图标

1. 将SVG文件放入 `src/assets/svgs/` 文件夹
2. 在 `src/components/SvgIcon/index.tsx` 中添加导入：
   ```tsx
   import newIconSvg from '../../assets/svgs/new-icon.svg?raw';
   ```
3. 在 `svgMap` 中添加映射：
   ```tsx
   const svgMap: Record<string, string> = {
     // ... 其他图标
     'new-icon': newIconSvg,
   };
   ```

## 颜色修改原理

组件会自动替换SVG中的 `fill` 和 `stroke` 属性，但会保留 `fill="none"` 的设置。这样可以确保大部分SVG图标都能正确显示指定的颜色。

## 注意事项

- SVG文件名应使用kebab-case命名（如：`my-icon.svg`）
- 确保SVG文件格式正确，包含完整的SVG标签
- 如果SVG使用了复杂的渐变或多色设计，颜色修改可能不会完全生效
- 组件会显示错误提示（?）当指定的图标不存在时
