import React, { useState, useRef } from "react";
import { Button, Input, QRCode as AntdQRCode, message } from "antd";
import { EyeOutlined } from "@ant-design/icons";
import copy from "copy-to-clipboard";
import styles from "./index.module.less";

// 图片资源常量
const copyIcon =
  "http://localhost:3845/assets/0dc90125d6f43e9679dec5c3d6141b90dfadb5eb.svg";

// 下载图片设计资源
const backgroundImage =
  "http://localhost:3845/assets/61f9d25aeea7a84839fe36cf1160f2d5115d3cd0.png";
const qrCodeImage =
  "http://localhost:3845/assets/6c44ec5abb9363daeb54ad0f602eba3a91622448.svg";
const facebookIcon =
  "http://localhost:3845/assets/2019e8e00551463383bb27c0b77dd1ceaf161582.svg";
const xiaohongshuIcon =
  "http://localhost:3845/assets/54c91e950f8480207813587350233d829b1ce3b2.png";
const tiktokIcon =
  "http://localhost:3845/assets/72c48827e037056e8a1fb0d7279688ebdf71eeef.svg";
const whatsappIcon =
  "http://localhost:3845/assets/78d1450f760131394c33256cde41770f9bdd0827.svg";

const QRCode: React.FC = () => {
  const [title, setTitle] = useState("分享并解锁奖励");
  const [shareUrl, setShareUrl] = useState("https://exampleqr.com");
  const qrRef = useRef<any>(null);

  // 复制链接到剪贴板
  const handleCopyUrl = () => {
    if (copy(shareUrl)) {
      message.success("链接已复制到剪贴板");
    } else {
      message.error("复制失败，请手动复制");
    }
  };

  // 下载二维码图片 - 根据Figma设计生成canvas图片
  const handleDownload = async () => {
    try {
      // 创建canvas
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");
      if (!ctx) return;

      // 设置canvas尺寸 (根据Figma设计)
      canvas.width = 433;
      canvas.height = 715;

      // 加载背景图片
      const bgImg = new Image();
      bgImg.crossOrigin = "anonymous";

      await new Promise((resolve, reject) => {
        bgImg.onload = resolve;
        bgImg.onerror = reject;
        bgImg.src = backgroundImage;
      });

      // 绘制背景
      ctx.drawImage(bgImg, 0, -28, 525.324, 743);

      // 绘制二维码白色背景
      ctx.fillStyle = "#ffffff";
      ctx.roundRect(52, 161, 329, 329, 24);
      ctx.fill();

      // 获取当前二维码的canvas
      const qrCanvas = qrRef.current?.querySelector("canvas");
      if (qrCanvas) {
        ctx.drawImage(qrCanvas, 84, 193, 265, 265);
      }

      // 绘制标题文字
      ctx.fillStyle = "#ffffff";
      ctx.font = "600 30px PingFang SC, sans-serif";
      ctx.textAlign = "center";
      ctx.letterSpacing = "0.3px";

      // 计算文字位置 (top: 80px, 居中)
      const textX = canvas.width / 2;
      const textY = 89;
      ctx.fillText(title, textX, textY);

      // 加载并绘制社交媒体图标
      const socialIcons = [
        { src: facebookIcon, x: 116, y: 525 },
        { src: xiaohongshuIcon, x: 116 + 32 + 24, y: 525 },
        { src: tiktokIcon, x: 116 + 64 + 48, y: 525 },
        { src: whatsappIcon, x: 116 + 96 + 72, y: 525 },
      ];

      for (const icon of socialIcons) {
        const img = new Image();
        img.crossOrigin = "anonymous";

        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
          img.src = icon.src;
        });

        ctx.drawImage(img, icon.x, icon.y, 32, 32);
      }

      // 下载图片
      const url = canvas.toDataURL("image/png");
      const a = document.createElement("a");
      a.download = `${title || "二维码"}.png`;
      a.href = url;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      message.success("二维码图片下载成功");
    } catch (error) {
      console.error("下载失败:", error);
      message.error("下载失败，请重试");
    }
  };

  // 打开预览页面
  const handlePreview = () => {
    // 创建预览页面的URL，传递必要的参数
    const previewUrl = `/qr-preview?title=${encodeURIComponent(
      title
    )}&url=${encodeURIComponent(shareUrl)}`;
    window.open(previewUrl);
  };

  return (
    <div className={styles.container}>
      {/* 头部区域 */}
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>二维码</h1>
        </div>
        <div className={styles.actionButtons}>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            onClick={handlePreview}
            className={styles.previewButton}
          >
            预览页面
          </Button>
          <Button className={styles.downloadButton} onClick={handleDownload}>
            下载图片
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className={styles.content}>
        <div className={styles.leftPanel}>
          {/* 标题输入框 */}
          <div className={styles.inputField}>
            <label className={styles.label}>标题</label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className={styles.titleInput}
            />
          </div>

          {/* 二维码显示区域 */}
          <div className={styles.qrCodeContainer} ref={qrRef}>
            <AntdQRCode
              value={shareUrl}
              size={265}
              style={{ backgroundColor: "#f6f6f6", border: 0 }}
            />
          </div>

          {/* 分享链接输入框和复制按钮 */}
          <div className={styles.shareSection}>
            <div className={styles.inputField}>
              <label className={styles.label}>分享链接</label>
              <div className={styles.shareInputContainer}>
                <Input
                  value={shareUrl}
                  onChange={(e) => setShareUrl(e.target.value)}
                  className={styles.shareInput}
                />
                <Button
                  icon={
                    <img
                      src={copyIcon}
                      alt="copy"
                      className={styles.copyIcon}
                    />
                  }
                  onClick={handleCopyUrl}
                  className={styles.copyButton}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QRCode;
