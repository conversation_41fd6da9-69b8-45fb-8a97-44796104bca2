import React, { useState, useRef } from "react";
import { Button, Input, QRCode as AntdQRCode, message } from "antd";
import { EyeOutlined } from "@ant-design/icons";
import copy from "copy-to-clipboard";
import styles from "./index.module.less";

// 图片资源常量
const copyIcon =
  "http://localhost:3845/assets/0dc90125d6f43e9679dec5c3d6141b90dfadb5eb.svg";

const QRCode: React.FC = () => {
  const [title, setTitle] = useState("分享并解锁奖励");
  const [shareUrl, setShareUrl] = useState("https://exampleqr.com");
  const qrRef = useRef<any>(null);

  // 复制链接到剪贴板
  const handleCopyUrl = () => {
    if (copy(shareUrl)) {
      message.success("链接已复制到剪贴板");
    } else {
      message.error("复制失败，请手动复制");
    }
  };

  // 下载二维码图片
  const handleDownload = () => {
    if (qrRef.current) {
      const canvas = qrRef.current.querySelector("canvas");
      if (canvas) {
        const url = canvas.toDataURL();
        const a = document.createElement("a");
        a.download = `${title || "二维码"}.png`;
        a.href = url;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        message.success("二维码图片下载成功");
      }
    }
  };

  // 打开预览页面
  const handlePreview = () => {
    // 创建预览页面的URL，传递必要的参数
    const previewUrl = `/qr-preview?title=${encodeURIComponent(
      title
    )}&url=${encodeURIComponent(shareUrl)}`;
    window.open(previewUrl);
  };

  return (
    <div className={styles.container}>
      {/* 头部区域 */}
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>二维码</h1>
        </div>
        <div className={styles.actionButtons}>
          <Button
            type="primary"
            icon={<EyeOutlined />}
            onClick={handlePreview}
            className={styles.previewButton}
          >
            预览页面
          </Button>
          <Button className={styles.downloadButton} onClick={handleDownload}>
            下载图片
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className={styles.content}>
        <div className={styles.leftPanel}>
          {/* 标题输入框 */}
          <div className={styles.inputField}>
            <label className={styles.label}>标题</label>
            <Input
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className={styles.titleInput}
            />
          </div>

          {/* 二维码显示区域 */}
          <div className={styles.qrCodeContainer} ref={qrRef}>
            <AntdQRCode
              value={shareUrl}
              size={265}
              style={{ backgroundColor: "#f6f6f6", border }}
            />
          </div>

          {/* 分享链接输入框和复制按钮 */}
          <div className={styles.shareSection}>
            <div className={styles.inputField}>
              <label className={styles.label}>分享链接</label>
              <div className={styles.shareInputContainer}>
                <Input
                  value={shareUrl}
                  onChange={(e) => setShareUrl(e.target.value)}
                  className={styles.shareInput}
                />
                <Button
                  icon={
                    <img
                      src={copyIcon}
                      alt="copy"
                      className={styles.copyIcon}
                    />
                  }
                  onClick={handleCopyUrl}
                  className={styles.copyButton}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QRCode;
